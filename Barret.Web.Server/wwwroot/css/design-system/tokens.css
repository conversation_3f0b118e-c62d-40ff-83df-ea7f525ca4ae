:root {
  /* ===== COLOR SYSTEM ===== */
  /* Primary Colors - Based on current black/white system */
  --color-primary: #000000;
  --color-primary-hover: #1f2937;
  --color-primary-contrast: #ffffff;
  
  /* Secondary Colors */
  --color-secondary: #ffffff;
  --color-secondary-hover: #f9fafb;
  --color-secondary-contrast: #000000;
  
  /* Semantic Colors */
  --color-success: #22c55e;
  --color-success-hover: #16a34a;
  --color-warning: #f59e0b;
  --color-warning-hover: #d97706;
  --color-danger: #ef4444;
  --color-danger-hover: #dc2626;
  
  /* Grayscale Palette - Matching current software.scss */
  --color-gray-50: #ffffff;
  --color-gray-100: #f9fafb;
  --color-gray-200: #f3f4f6;
  --color-gray-300: #e5e7eb;
  --color-gray-400: #d1d5db;
  --color-gray-500: #9ca3af;
  --color-gray-600: #6b7280;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;
  
  /* ===== SPACING SYSTEM ===== */
  --spacing-0: 0;
  --spacing-1: 0.25rem;    /* 4px */
  --spacing-2: 0.5rem;     /* 8px */
  --spacing-3: 0.75rem;    /* 12px */
  --spacing-4: 1rem;       /* 16px */
  --spacing-5: 1.25rem;    /* 20px */
  --spacing-6: 1.5rem;     /* 24px */
  --spacing-8: 2rem;       /* 32px */
  --spacing-10: 2.5rem;    /* 40px */
  --spacing-12: 3rem;      /* 48px */
  --spacing-16: 4rem;      /* 64px */
  
  /* ===== BORDER RADIUS ===== */
  --radius-none: 0;
  --radius-sm: 0.375rem;   /* 6px */
  --radius-md: 0.5rem;     /* 8px */
  --radius-lg: 0.75rem;    /* 12px */
  --radius-xl: 1rem;       /* 16px */
  --radius-2xl: 1.5rem;    /* 24px */
  --radius-full: 9999px;
  
  /* ===== SHADOWS ===== */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -1px rgb(0 0 0 / 0.06);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -2px rgb(0 0 0 / 0.05);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 10px 10px -5px rgb(0 0 0 / 0.04);
  
  /* ===== TYPOGRAPHY ===== */
  --font-size-xs: 0.75rem;     /* 12px */
  --font-size-sm: 0.875rem;    /* 14px */
  --font-size-base: 1rem;      /* 16px */
  --font-size-lg: 1.125rem;    /* 18px */
  --font-size-xl: 1.25rem;     /* 20px */
  --font-size-2xl: 1.5rem;     /* 24px */
  --font-size-3xl: 1.875rem;   /* 30px */
  
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* ===== TRANSITIONS ===== */
  --transition-fast: 0.15s ease-out;
  --transition-normal: 0.2s ease-out;
  --transition-slow: 0.3s ease-out;
}

/* Dark Theme Overrides */
[data-theme="dark"] {
  --color-primary: #ffffff;
  --color-primary-hover: #f3f4f6;
  --color-primary-contrast: #000000;
  
  --color-secondary: #1f2937;
  --color-secondary-hover: #374151;
  --color-secondary-contrast: #ffffff;
  
  --color-gray-50: #111827;
  --color-gray-100: #1f2937;
  --color-gray-200: #374151;
  --color-gray-300: #4b5563;
  --color-gray-400: #6b7280;
  --color-gray-500: #9ca3af;
  --color-gray-600: #d1d5db;
  --color-gray-700: #e5e7eb;
  --color-gray-800: #f3f4f6;
  --color-gray-900: #ffffff;
}
