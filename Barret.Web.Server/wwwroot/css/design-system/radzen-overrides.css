/* ===== RADZEN BUTTON OVERRIDES ===== */
.rz-button {
  /* Reset <PERSON> defaults */
  border: none !important;
  border-radius: var(--radius-full) !important;
  font-weight: var(--font-weight-medium) !important;
  transition: all var(--transition-normal) !important;
  font-family: inherit !important;

  /* Apply our button system */
  &.btn-primary {
    background-color: var(--color-primary) !important;
    color: var(--color-primary-contrast) !important;

    &:hover:not(.rz-state-disabled) {
      background-color: var(--color-primary-hover) !important;
      transform: translateY(-1px) !important;
      box-shadow: var(--shadow-md) !important;
    }

    &:active:not(.rz-state-disabled) {
      transform: translateY(0) !important;
      box-shadow: var(--shadow-sm) !important;
    }
  }

  &.btn-secondary {
    background-color: var(--color-secondary) !important;
    color: var(--color-secondary-contrast) !important;
    border: 1px solid var(--color-gray-300) !important;

    &:hover:not(.rz-state-disabled) {
      background-color: var(--color-secondary-hover) !important;
      border-color: var(--color-gray-400) !important;
    }
  }

  /* Size classes */
  &.btn-sm {
    padding: var(--spacing-2) var(--spacing-3) !important;
    font-size: var(--font-size-sm) !important;
    height: 2rem !important;
    min-width: 4rem !important;
  }

  &.btn-md {
    padding: var(--spacing-3) var(--spacing-4) !important;
    font-size: var(--font-size-sm) !important;
    height: 2.5rem !important;
    min-width: 5rem !important;
  }

  &.btn-lg {
    padding: var(--spacing-4) var(--spacing-6) !important;
    font-size: var(--font-size-base) !important;
    height: 3rem !important;
    min-width: 6rem !important;
  }
}

/* ===== RADZEN DIALOG OVERRIDES ===== */
.rz-dialog {
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-xl) !important;
  border: 1px solid var(--color-gray-200) !important;
  background: var(--color-secondary) !important;
  max-width: 95vw !important;
  max-height: 95vh !important;
  overflow: hidden !important;

  .rz-dialog-content {
    padding: 0 !important;
    border-radius: inherit !important;
    background: inherit !important;
  }

  .rz-dialog-titlebar {
    background: var(--color-gray-50) !important;
    border-bottom: 1px solid var(--color-gray-200) !important;
    padding: var(--spacing-6) !important;

    .rz-dialog-title {
      color: var(--color-gray-900) !important;
      font-weight: var(--font-weight-semibold) !important;
      font-size: var(--font-size-lg) !important;
    }
  }
}

.rz-dialog-mask {
  background-color: rgb(0 0 0 / 0.5) !important;
  backdrop-filter: blur(2px) !important;
}

/* ===== RADZEN FORM CONTROLS ===== */
.rz-textbox, .rz-textarea, .rz-numeric input, .rz-dropdown {
  border: 1px solid var(--color-gray-300) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--spacing-3) var(--spacing-4) !important;
  font-size: var(--font-size-sm) !important;
  background-color: var(--color-secondary) !important;
  color: var(--color-secondary-contrast) !important;
  transition: all var(--transition-normal) !important;
  font-family: inherit !important;

  &:focus {
    border-color: var(--color-primary) !important;
    box-shadow: 0 0 0 3px rgb(17 24 39 / 0.1) !important;
    outline: none !important;
  }

  &:hover:not(:focus) {
    border-color: var(--color-gray-400) !important;
  }

  &::placeholder {
    color: var(--color-gray-500) !important;
  }
}

/* ===== RADZEN DATA GRID ===== */
.rz-datatable {
  border: 1px solid var(--color-gray-200) !important;
  border-radius: var(--radius-xl) !important;
  overflow: hidden !important;
  background-color: var(--color-secondary) !important;
  box-shadow: var(--shadow-sm) !important;

  .rz-datatable-header {
    background-color: var(--color-gray-50) !important;
    border-bottom: 1px solid var(--color-gray-200) !important;

    th {
      padding: var(--spacing-4) !important;
      font-weight: var(--font-weight-medium) !important;
      color: var(--color-gray-700) !important;
      font-size: var(--font-size-sm) !important;
      border-right: 1px solid var(--color-gray-200) !important;

      &:last-child {
        border-right: none !important;
      }
    }
  }

  .rz-datatable-data {
    td {
      padding: var(--spacing-4) !important;
      border-bottom: 1px solid var(--color-gray-100) !important;
      border-right: 1px solid var(--color-gray-100) !important;
      font-size: var(--font-size-sm) !important;
      color: var(--color-gray-900) !important;

      &:last-child {
        border-right: none !important;
      }
    }

    tr:hover {
      background-color: var(--color-gray-50) !important;
    }

    tr:last-child td {
      border-bottom: none !important;
    }
  }
}

/* ===== RADZEN CARD ===== */
.rz-card {
  background-color: var(--color-secondary) !important;
  border-radius: var(--radius-xl) !important;
  border: 1px solid var(--color-gray-200) !important;
  box-shadow: var(--shadow-sm) !important;
  transition: all var(--transition-normal) !important;
  overflow: hidden !important;

  &:hover {
    box-shadow: var(--shadow-md) !important;
    border-color: var(--color-gray-300) !important;
  }

  .rz-card-header {
    padding: var(--spacing-6) var(--spacing-6) var(--spacing-4) !important;
    border-bottom: 1px solid var(--color-gray-200) !important;
    background-color: transparent !important;
  }

  .rz-card-content {
    padding: var(--spacing-6) !important;
  }

  .rz-card-footer {
    padding: var(--spacing-4) var(--spacing-6) var(--spacing-6) !important;
    border-top: 1px solid var(--color-gray-200) !important;
    background-color: var(--color-gray-50) !important;
  }
}
