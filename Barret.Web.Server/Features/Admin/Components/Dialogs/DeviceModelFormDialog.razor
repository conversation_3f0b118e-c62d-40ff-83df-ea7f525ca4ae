@using Barret.Shared.DTOs.Devices
@using Barret.Core.Areas.Devices.Enums
@using Ra<PERSON>zen
@inject Radzen.DialogService DialogService

@* Device Model Form Dialog Component *@

<div class="barret-form-dialog">
    <div class="barret-dialog-header">
        <div class="flex items-center">
            <svg class="h-6 w-6 mr-3 text-blue-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect width="7" height="7" x="3" y="3" rx="1"></rect>
                <rect width="7" height="7" x="14" y="3" rx="1"></rect>
                <rect width="7" height="7" x="14" y="14" rx="1"></rect>
                <rect width="7" height="7" x="3" y="14" rx="1"></rect>
            </svg>
            <h3 class="text-lg font-semibold text-gray-900 m-0">@Title</h3>
        </div>
    </div>
    
    <div class="barret-dialog-content">
        @if (HasError)
        {
            <div class="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
                <div class="flex items-center">
                    <i class="bi bi-exclamation-triangle text-red-500 mr-2"></i>
                    <span class="text-red-700 text-sm">@ErrorMessage</span>
                </div>
            </div>
        }
        
        <div class="mb-4">
            <label for="deviceModelName" class="block text-sm font-medium text-gray-700 mb-1">Model Name</label>
            <input type="text" id="deviceModelName"
                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 @(HasNameError ? "border-red-300 focus:ring-red-500 focus:border-red-500" : "")"
                   @bind="DeviceModelName"
                   @bind:event="oninput"
                   placeholder="Enter model name" />
            @if (HasNameError)
            {
                <p class="mt-1 text-sm text-red-600">@NameError</p>
            }
        </div>

        <div class="mb-4">
            <label for="deviceRole" class="block text-sm font-medium text-gray-700 mb-1">Device Role</label>
            <RadzenDropDown @bind-Value="@SelectedDeviceRole"
                           Data="@DeviceRoles"
                           Placeholder="Select a device role..."
                           AllowClear="true"
                           class="@($"form-input w-full {(HasRoleError ? "form-input-error" : "")}")">
                <Template>
                    <div class="py-1">
                        <span class="font-medium">@context</span>
                    </div>
                </Template>
            </RadzenDropDown>
            @if (HasRoleError)
            {
                <p class="mt-1 text-sm text-red-600">@RoleError</p>
            }
        </div>
    </div>
    
    <div class="barret-dialog-footer">
        <div class="flex gap-2 justify-end">
            <RadzenButton Text="Cancel"
                          ButtonStyle="ButtonStyle.Secondary"
                          Click="@CancelAsync"
                          Disabled="@IsProcessing"
                          class="btn btn-md btn-secondary" />
            <RadzenButton Text="Save"
                          Icon="save"
                          ButtonStyle="ButtonStyle.Primary"
                          Click="@SaveAsync"
                          Disabled="@(!IsValid || IsProcessing)"
                          class="btn btn-md btn-primary">
                @if (IsProcessing)
                {
                    <i class="bi bi-arrow-clockwise animate-spin mr-2"></i>
                }
                Save
            </RadzenButton>
        </div>
    </div>
</div>

@code {
    [Parameter] public DeviceModelInfo? DeviceModel { get; set; }
    [Parameter] public ManufacturerInfo? Manufacturer { get; set; }
    [Parameter] public bool IsEditing { get; set; } = false;
    [Parameter] public IEnumerable<DeviceRole> DeviceRoles { get; set; } = Enum.GetValues<DeviceRole>();

    private string deviceModelName = "";
    private DeviceRole? selectedDeviceRole;
    private string nameError = "";
    private string roleError = "";
    private string errorMessage = "";
    private bool isProcessing = false;

    public string Title => IsEditing ? "Edit Device Model" : "Add Device Model";
    public bool HasError => !string.IsNullOrEmpty(errorMessage);
    public string ErrorMessage => errorMessage;
    public bool HasNameError => !string.IsNullOrEmpty(nameError);
    public string NameError => nameError;
    public bool HasRoleError => !string.IsNullOrEmpty(roleError);
    public string RoleError => roleError;
    public bool IsProcessing => isProcessing;
    public bool IsValid => !string.IsNullOrWhiteSpace(deviceModelName) && selectedDeviceRole.HasValue && !HasNameError && !HasRoleError;

    public string DeviceModelName
    {
        get => deviceModelName;
        set
        {
            deviceModelName = value;
            ValidateName();
        }
    }

    public DeviceRole? SelectedDeviceRole
    {
        get => selectedDeviceRole;
        set
        {
            selectedDeviceRole = value;
            ValidateRole();
        }
    }

    protected override void OnInitialized()
    {
        if (DeviceModel != null)
        {
            deviceModelName = DeviceModel.Name ?? "";
            selectedDeviceRole = DeviceModel.DeviceRole;
        }
    }

    private void ValidateName()
    {
        nameError = "";
        
        if (string.IsNullOrWhiteSpace(deviceModelName))
        {
            nameError = "Model name is required.";
        }
        else if (deviceModelName.Length < 2)
        {
            nameError = "Model name must be at least 2 characters long.";
        }
        else if (deviceModelName.Length > 100)
        {
            nameError = "Model name cannot exceed 100 characters.";
        }
    }

    private void ValidateRole()
    {
        roleError = "";
        
        if (!selectedDeviceRole.HasValue)
        {
            roleError = "Device role is required.";
        }
    }

    private async Task SaveAsync()
    {
        try
        {
            isProcessing = true;
            errorMessage = "";
            
            ValidateName();
            ValidateRole();
            
            if (!IsValid)
            {
                return;
            }

            var result = new DeviceModelFormResult
            {
                Name = deviceModelName.Trim(),
                DeviceRole = selectedDeviceRole!.Value,
                Manufacturer = Manufacturer,
                IsEditing = IsEditing,
                OriginalDeviceModel = DeviceModel
            };

            DialogService.Close(result);
        }
        catch (Exception ex)
        {
            errorMessage = $"Error saving device model: {ex.Message}";
        }
        finally
        {
            isProcessing = false;
        }
    }

    private async Task CancelAsync()
    {
        DialogService.Close(null);
    }

    public class DeviceModelFormResult
    {
        public string Name { get; set; } = "";
        public DeviceRole DeviceRole { get; set; }
        public ManufacturerInfo? Manufacturer { get; set; }
        public bool IsEditing { get; set; }
        public DeviceModelInfo? OriginalDeviceModel { get; set; }
    }
}
