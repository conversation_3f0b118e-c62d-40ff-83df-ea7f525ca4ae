@using Barret.Shared.DTOs.Devices
@using Barret.Web.Server.Extensions
@using Barret.Web.Server.Features.Vehicles.Editor.Components.Devices.ViewModels
@inherits Barret.Web.Server.Features.Shared.ViewBase<DeviceManagerViewModel>
@inject DialogService DialogService

<div class="device-manager-section">
    <div class="flex justify-between items-center mb-4">
        <h5 class="text-lg font-semibold text-gray-900 mb-0">Devices</h5>
        <RadzenButton Icon="add"
                     Text="Add Device"
                     ButtonStyle="ButtonStyle.Primary"
                     Size="ButtonSize.Small"
                     Click="@(() => ViewModel.AddDeviceCommand.Execute())"
                     class="btn btn-md btn-primary" />
    </div>

    @if (ViewModel.Devices?.Any() == true)
    {
        <RadzenDataGrid @ref="devicesGrid"
                       Data="@ViewModel.Devices"
                       TItem="DeviceDto"
                       AllowFiltering="true"
                       AllowSorting="true"
                       AllowPaging="true"
                       PageSize="10"
                       PagerHorizontalAlign="HorizontalAlign.Left"
                       ShowPagingSummary="true"
                       AllowRowSelectOnRowClick="false"
                       class="barret-grid">
            
            <Columns>
                <RadzenDataGridColumn TItem="DeviceDto" 
                                    Property="Name" 
                                    Title="Device Name"
                                    Width="250px">
                    <Template Context="device">
                        <div class="flex items-center space-x-3">
                            <span class="font-medium text-gray-900">@device.Name</span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @ViewModel.GetDeviceStatusBadgeClass(device)">
                                @ViewModel.GetDeviceStatusText(device)
                            </span>
                        </div>
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn TItem="DeviceDto"
                                    Property="ModelName"
                                    Title="Model"
                                    Width="200px">
                    <Template Context="device">
                        @if (!string.IsNullOrEmpty(device.ModelName))
                        {
                            <span class="text-gray-700">@device.ModelName</span>
                        }
                        else
                        {
                            <span class="text-gray-400 italic">No model</span>
                        }
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn TItem="DeviceDto" 
                                    Property="DeviceRole" 
                                    Title="Role"
                                    Width="150px">
                    <Template Context="device">
                        <span class="text-sm text-gray-600">@device.DeviceRole</span>
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn TItem="DeviceDto" 
                                    Title="Connections"
                                    Width="100px"
                                    Sortable="false">
                    <Template Context="device">
                        <span class="text-sm text-blue-600 font-medium">@ViewModel.GetConnectionCount(device)</span>
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn TItem="DeviceDto" 
                                    Title="Alarms"
                                    Width="100px"
                                    Sortable="false">
                    <Template Context="device">
                        <span class="text-sm text-orange-600 font-medium">@ViewModel.GetAlarmCount(device)</span>
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn TItem="DeviceDto" 
                                    Title="Actions" 
                                    Sortable="false" 
                                    Filterable="false"
                                    Width="150px">
                    <Template Context="device">
                        <div class="flex space-x-2">
                            <RadzenButton Icon="edit"
                                         ButtonStyle="ButtonStyle.Light"
                                         Variant="Variant.Outlined"
                                         Size="ButtonSize.ExtraSmall"
                                         Click="@(() => ViewModel.EditDeviceCommand.Execute(device))"
                                         title="Edit Device"
                                         class="barret-btn barret-action-btn" />
                            <RadzenButton Icon="settings"
                                         ButtonStyle="ButtonStyle.Info"
                                         Variant="Variant.Outlined"
                                         Size="ButtonSize.ExtraSmall"
                                         Click="@(() => ShowConnectionManager(device))"
                                         title="Manage Connections"
                                         class="barret-btn barret-action-btn" />
                            <RadzenButton Icon="delete"
                                         ButtonStyle="ButtonStyle.Danger"
                                         Variant="Variant.Outlined"
                                         Size="ButtonSize.ExtraSmall"
                                         Click="@(() => ViewModel.RemoveDeviceCommand.Execute(device))"
                                         title="Remove Device"
                                         class="barret-btn barret-action-btn" />
                        </div>
                    </Template>
                </RadzenDataGridColumn>
            </Columns>

            <!-- Expandable row template for connections -->
            <Template Context="device">
                <div class="p-4 bg-gray-50 border-t border-gray-200">
                    <ConnectionManagerView ParentDevice="@device"
                                         OnDeviceChanged="@HandleDeviceUpdated" />
                </div>
            </Template>
        </RadzenDataGrid>
    }
    else
    {
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div class="flex items-center">
                <i class="bi bi-info-circle text-blue-600 mr-3"></i>
                <span class="text-blue-800">No devices configured. Click "Add Device" to create one.</span>
            </div>
        </div>
    }
</div>

@* Device Edit Dialog *@
@if (ViewModel.IsAddDeviceDialogVisible && ViewModel.SelectedDevice != null)
{
    <RadzenDialog @bind-Visible="@ViewModel.IsAddDeviceDialogVisible">
        <div class="p-6 min-w-[600px]">
            <h4 class="text-lg font-semibold text-gray-900 mb-4">
                @(ViewModel.Devices.Contains(ViewModel.SelectedDevice) ? "Edit Device" : "Add New Device")
            </h4>
            
            <div class="space-y-4">
                <RadzenFormField Text="Device Name" Variant="Variant.Outlined">
                    <RadzenTextBox @bind-Value="@ViewModel.SelectedDevice.Name"
                                 Placeholder="Enter device name..."
                                 class="form-input w-full" />
                </RadzenFormField>

                <RadzenFormField Text="Description" Variant="Variant.Outlined">
                    <RadzenTextArea @bind-Value="@ViewModel.SelectedDeviceDescription"
                                  Placeholder="Enter device description..."
                                  Rows="3"
                                  class="form-input w-full" />
                </RadzenFormField>

                <div class="flex items-center space-x-2">
                    <RadzenCheckBox @bind-Value="@ViewModel.SelectedDeviceIsActive"
                                   Name="isActive" />
                    <RadzenLabel Text="Active Device" Component="isActive" class="text-sm text-gray-700" />
                </div>
            </div>

            <div class="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200">
                <RadzenButton Text="Cancel" 
                             ButtonStyle="ButtonStyle.Light"
                             Click="@(() => ViewModel.CancelDeviceCommand.Execute())"
                             class="barret-btn barret-secondary-btn" />
                <RadzenButton Text="Save" 
                             ButtonStyle="ButtonStyle.Primary"
                             Click="@(() => ViewModel.SaveDeviceCommand.Execute())"
                             class="barret-btn barret-primary-btn" />
            </div>
        </div>
    </RadzenDialog>
}
