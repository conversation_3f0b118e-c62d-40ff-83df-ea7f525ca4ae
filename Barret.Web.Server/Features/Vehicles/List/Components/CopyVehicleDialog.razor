@using <PERSON><PERSON><PERSON>
@using Ra<PERSON>zen.Blazor
@inject Radzen.DialogService DialogService

@* Copy Vehicle Form Dialog Component *@

<div class="dialog">
    <div class="dialog-header">
        <div class="flex items-center">
            <i class="bi bi-copy text-blue-600 mr-2 text-xl"></i>
            <h3 class="dialog-title">Copy Vehicle</h3>
        </div>
    </div>

    <div class="dialog-content dialog-content-centered">
        <div class="text-center mb-4">
            <div class="dialog-icon dialog-icon-info">
                <i class="bi bi-copy text-2xl"></i>
            </div>
        </div>
        <p class="text-gray-700 mb-4">Please enter a name for the copied vehicle:</p>
        <div class="form-group">
            <label for="vehicleName" class="form-label">Vehicle Name</label>
            <RadzenTextBox Value="@VehicleName"
                          ValueChanged="@((value) => VehicleName = value)"
                          Placeholder="Enter vehicle name"
                          class="form-input" />
        </div>
    </div>

    <div class="dialog-footer">
        <div class="flex gap-2 justify-end">
            <RadzenButton Text="Cancel"
                         Icon="cancel"
                         ButtonStyle="ButtonStyle.Secondary"
                         Click="@CancelAsync"
                         class="btn btn-md btn-secondary" />
            <RadzenButton Text="Copy"
                         Icon="content_copy"
                         ButtonStyle="ButtonStyle.Primary"
                         Click="@CopyAsync"
                         Disabled="@(string.IsNullOrWhiteSpace(VehicleName) || IsProcessing)"
                         class="btn btn-md btn-primary">
                @if (IsProcessing)
                {
                    <i class="bi bi-arrow-clockwise animate-spin mr-2"></i>
                }
                Copy
            </RadzenButton>
        </div>
    </div>
</div>
