@using Microsoft.AspNetCore.Components.Web
@using <PERSON><PERSON>zen
@inject Radzen.DialogService DialogService

@* Reusable Info Dialog Component for Radzen Dialog System *@

<div class="barret-info-dialog">
    <div class="barret-dialog-content text-center">
        @if (!string.IsNullOrEmpty(Icon))
        {
            <div class="barret-dialog-icon @GetIconContainerClass() mb-4">
                <i class="@Icon @GetIconClass()"></i>
            </div>
        }
        
        <h3 class="text-lg font-semibold text-gray-900 mb-2">@Title</h3>
        
        @if (!string.IsNullOrEmpty(Message))
        {
            <p class="text-gray-600 mb-4">@Message</p>
        }
        
        @if (ChildContent != null)
        {
            <div class="mb-4">
                @ChildContent
            </div>
        }
    </div>
    
    <div class="barret-dialog-footer">
        <div class="flex gap-2 justify-center">
            <RadzenButton Text="@OkText"
                          ButtonStyle="@GetButtonStyle()"
                          Click="@OkAsync"
                          class="btn btn-md btn-primary" />
        </div>
    </div>
</div>

@code {
    [Parameter] public string Title { get; set; } = "Information";
    [Parameter] public string Message { get; set; } = "";
    [Parameter] public string Icon { get; set; } = "bi bi-info-circle";
    [Parameter] public InfoDialogType Type { get; set; } = InfoDialogType.Info;
    [Parameter] public string OkText { get; set; } = "OK";
    [Parameter] public RenderFragment? ChildContent { get; set; }
    [Parameter] public EventCallback OnOk { get; set; }

    private async Task OkAsync()
    {
        if (OnOk.HasDelegate)
        {
            await OnOk.InvokeAsync();
        }
        else
        {
            // Default behavior: close dialog
            DialogService.Close(true);
        }
    }

    private string GetIconContainerClass()
    {
        return Type switch
        {
            InfoDialogType.Success => "barret-dialog-icon-success",
            InfoDialogType.Warning => "barret-dialog-icon-warning",
            InfoDialogType.Error => "barret-dialog-icon-danger",
            InfoDialogType.Info => "barret-dialog-icon-info",
            _ => "barret-dialog-icon-info"
        };
    }

    private string GetIconClass()
    {
        return Type switch
        {
            InfoDialogType.Success => "h-6 w-6 text-green-500",
            InfoDialogType.Warning => "h-6 w-6 text-yellow-500",
            InfoDialogType.Error => "h-6 w-6 text-red-500",
            InfoDialogType.Info => "h-6 w-6 text-blue-500",
            _ => "h-6 w-6 text-blue-500"
        };
    }

    private ButtonStyle GetButtonStyle()
    {
        return Type switch
        {
            InfoDialogType.Success => ButtonStyle.Success,
            InfoDialogType.Warning => ButtonStyle.Warning,
            InfoDialogType.Error => ButtonStyle.Danger,
            InfoDialogType.Info => ButtonStyle.Primary,
            _ => ButtonStyle.Primary
        };
    }

    public enum InfoDialogType
    {
        Info,
        Success,
        Warning,
        Error
    }
}
