@using Microsoft.AspNetCore.Components.Web
@using <PERSON><PERSON><PERSON>
@inject Radzen.DialogService DialogService

@* Reusable Info Dialog Component for Radzen Dialog System *@

<div class="dialog">
    <div class="dialog-content dialog-content-centered">
        @if (!string.IsNullOrEmpty(Icon))
        {
            <div class="dialog-icon @GetIconContainerClass()">
                <i class="@Icon @GetIconClass()"></i>
            </div>
        }

        <h3 class="dialog-title mb-2">@Title</h3>

        @if (!string.IsNullOrEmpty(Message))
        {
            <p class="text-gray-600 mb-4">@Message</p>
        }

        @if (ChildContent != null)
        {
            <div class="mb-4">
                @ChildContent
            </div>
        }
    </div>

    <div class="dialog-footer">
        <div class="flex gap-2 justify-center">
            <RadzenButton Text="@OkText"
                          ButtonStyle="@GetButtonStyle()"
                          Click="@OkAsync"
                          class="btn btn-md @GetButtonClass()" />
        </div>
    </div>
</div>

@code {
    [Parameter] public string Title { get; set; } = "Information";
    [Parameter] public string Message { get; set; } = "";
    [Parameter] public string Icon { get; set; } = "bi bi-info-circle";
    [Parameter] public InfoDialogType Type { get; set; } = InfoDialogType.Info;
    [Parameter] public string OkText { get; set; } = "OK";
    [Parameter] public RenderFragment? ChildContent { get; set; }
    [Parameter] public EventCallback OnOk { get; set; }

    private async Task OkAsync()
    {
        if (OnOk.HasDelegate)
        {
            await OnOk.InvokeAsync();
        }
        else
        {
            // Default behavior: close dialog
            DialogService.Close(true);
        }
    }

    private string GetIconContainerClass()
    {
        return Type switch
        {
            InfoDialogType.Success => "dialog-icon-success",
            InfoDialogType.Warning => "dialog-icon-warning",
            InfoDialogType.Error => "dialog-icon-danger",
            InfoDialogType.Info => "dialog-icon-info",
            _ => "dialog-icon-info"
        };
    }

    private string GetIconClass()
    {
        return Type switch
        {
            InfoDialogType.Success => "h-6 w-6",
            InfoDialogType.Warning => "h-6 w-6",
            InfoDialogType.Error => "h-6 w-6",
            InfoDialogType.Info => "h-6 w-6",
            _ => "h-6 w-6"
        };
    }

    private string GetButtonClass()
    {
        return Type switch
        {
            InfoDialogType.Success => "btn-success",
            InfoDialogType.Warning => "btn-warning",
            InfoDialogType.Error => "btn-danger",
            InfoDialogType.Info => "btn-primary",
            _ => "btn-primary"
        };
    }

    private ButtonStyle GetButtonStyle()
    {
        return Type switch
        {
            InfoDialogType.Success => ButtonStyle.Success,
            InfoDialogType.Warning => ButtonStyle.Warning,
            InfoDialogType.Error => ButtonStyle.Danger,
            InfoDialogType.Info => ButtonStyle.Primary,
            _ => ButtonStyle.Primary
        };
    }

    public enum InfoDialogType
    {
        Info,
        Success,
        Warning,
        Error
    }
}
